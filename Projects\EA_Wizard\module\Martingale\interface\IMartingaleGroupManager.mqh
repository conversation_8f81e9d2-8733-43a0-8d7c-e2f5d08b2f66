#property strict

#include "../MartingaleOrderGroup.mqh"
#include "../../mql4-lib-master/Collection/Vector.mqh"

//+------------------------------------------------------------------+
//| 馬丁格爾訂單組管理器介面                                         |
//+------------------------------------------------------------------+
interface IMartingaleGroupManager
{
public:
   // 添加訂單組
   virtual bool SetGroup(string id, MartingaleOrderGroup* group) = 0;
   
   // 刪除訂單組
   virtual bool RemoveGroup(string id) = 0;
   
   // 根據索引獲取訂單組
   virtual MartingaleOrderGroup* GetGroup(string id) = 0;
      
   // 關閉所有訂單組
   virtual bool CloseAllGroups() = 0;
   
   // 更新所有訂單組狀態
   virtual void UpdateAllGroups() = 0;
   
   // 獲取總盈虧
   virtual double GetTotalProfit() = 0;
   
   // 獲取訂單組總數
   virtual int GetGroupCount() = 0;
   
   // 檢查訂單組是否存在
   virtual bool GroupExists(int groupId) = 0;
   
   // 清空所有訂單組
   virtual void ClearAllGroups() = 0;
   
   // 獲取管理器統計信息
   virtual string GetStatistics() = 0;
   
   // 設置所有訂單組的止盈
   virtual void SetAllGroupsTP(double profit) = 0;
   
   // 設置所有訂單組的止損
   virtual void SetAllGroupsSL(double loss) = 0;
};
